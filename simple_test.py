#!/usr/bin/env python3
"""
Simple test to verify OpenManus components work.
"""

import asyncio
import sys

async def test_tools():
    """Test individual tools."""
    print("Testing individual tools...")
    
    try:
        from app.tool import Bash, Terminate, StrReplaceEditor
        
        # Test Terminate tool
        terminate_tool = Terminate()
        result = await terminate_tool.execute(status="success")
        print(f"✓ Terminate tool: {result}")
        
        # Test Bash tool with simple command
        bash_tool = Bash()
        result = await bash_tool.execute(command="echo 'Hello World'")
        print(f"✓ Bash tool: {result.output.strip()}")
        
        # Test StrReplaceEditor tool (view command)
        editor_tool = StrReplaceEditor()
        result = await editor_tool.execute(command="view", path="README.md")
        print(f"✓ StrReplaceEditor tool: File viewed successfully (length: {len(result)})")
        
        return True
        
    except Exception as e:
        print(f"✗ Tool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config_access():
    """Test configuration access."""
    print("\nTesting configuration access...")
    
    try:
        from app.config import config
        
        # Test basic config access
        llm_config = config.llm['default']
        print(f"✓ LLM config accessed: {llm_config.model}")
        
        # Test workspace path
        workspace = config.workspace_root
        print(f"✓ Workspace path: {workspace}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

async def test_tool_collection():
    """Test tool collection functionality."""
    print("\nTesting tool collection...")
    
    try:
        from app.tool import ToolCollection, Bash, Terminate
        
        # Create a tool collection
        tools = ToolCollection(Bash(), Terminate())
        print(f"✓ Tool collection created with {len(tools.tools)} tools")
        
        # Test getting a tool by name
        bash_tool = tools.get_tool("bash")
        print(f"✓ Retrieved bash tool: {bash_tool.name if bash_tool else 'None'}")
        
        # Test tool schemas
        schemas = tools.get_tool_schemas()
        print(f"✓ Tool schemas generated: {len(schemas)} schemas")
        
        return True
        
    except Exception as e:
        print(f"✗ Tool collection test failed: {e}")
        return False

async def test_file_operations():
    """Test file operations."""
    print("\nTesting file operations...")
    
    try:
        from app.tool import StrReplaceEditor
        import os
        
        editor = StrReplaceEditor()
        
        # Test creating a simple file
        test_content = "Hello OpenManus!\nThis is a test file."
        result = await editor.execute(
            command="create",
            path="test_file.txt",
            file_text=test_content
        )
        print(f"✓ File created: {result}")
        
        # Test reading the file
        result = await editor.execute(command="view", path="test_file.txt")
        print(f"✓ File read successfully")
        
        # Clean up
        if os.path.exists("test_file.txt"):
            os.remove("test_file.txt")
            print("✓ Test file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("OpenManus Simple Component Test")
    print("=" * 40)
    
    tests = [
        test_config_access,
        test_tools,
        test_tool_collection,
        test_file_operations,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All component tests passed!")
        print("\nOpenManus is ready to use!")
        print("\nTo run the full application:")
        print("1. Set your API key in config/config.toml")
        print("2. Run: python main.py")
    else:
        print("✗ Some tests failed.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
