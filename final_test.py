#!/usr/bin/env python3
"""
Final test to verify OpenManus is ready to run.
"""

import asyncio
import sys
import os
from pathlib import Path

async def test_basic_functionality():
    """Test basic functionality that should work."""
    print("Testing basic functionality...")
    
    try:
        # Test configuration
        from app.config import config
        llm_config = config.llm['default']
        print(f"✓ Configuration loaded: {llm_config.model}")
        
        # Test terminate tool (simplest tool)
        from app.tool import Terminate
        terminate_tool = Terminate()
        result = await terminate_tool.execute(status="success")
        print(f"✓ Terminate tool works: {result}")
        
        # Test tool collection
        from app.tool import ToolCollection
        tools = ToolCollection(terminate_tool)
        print(f"✓ Tool collection created with {len(tools.tools)} tool(s)")
        
        # Test file editor with absolute path
        from app.tool import StrReplaceEditor
        editor = StrReplaceEditor()
        
        # Use workspace directory for test file
        workspace = Path(config.workspace_root)
        test_file = workspace / "test.txt"
        
        # Create test file
        result = await editor.execute(
            command="create",
            path=str(test_file),
            file_text="Hello OpenManus Test!"
        )
        print(f"✓ File creation works")
        
        # Read test file
        result = await editor.execute(command="view", path=str(test_file))
        print(f"✓ File reading works")
        
        # Clean up
        if test_file.exists():
            test_file.unlink()
            print("✓ Test file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_imports():
    """Test that we can import agent classes."""
    print("\nTesting agent imports...")
    
    try:
        from app.agent import BaseAgent, ToolCallAgent
        print("✓ Base agent classes imported")
        
        # Test that we can import Manus (but not create it due to LLM dependency)
        from app.agent.manus import Manus
        print("✓ Manus agent class imported")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent import test failed: {e}")
        return False

def check_api_key():
    """Check if API key is configured."""
    print("\nChecking API key configuration...")
    
    try:
        from app.config import config
        llm_config = config.llm['default']
        
        if llm_config.api_key == "YOUR_OPENAI_API_KEY":
            print("⚠ API key not configured (using placeholder)")
            print("  To run the full application, edit config/config.toml and set your API key")
            return False
        else:
            print("✓ API key is configured")
            return True
            
    except Exception as e:
        print(f"✗ API key check failed: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions."""
    print("\n" + "=" * 50)
    print("OpenManus Setup Complete!")
    print("=" * 50)
    
    print("\nTo use OpenManus:")
    print("1. Configure your API key in config/config.toml:")
    print("   [llm]")
    print("   api_key = \"your-actual-openai-api-key\"")
    print("")
    print("2. Run the application:")
    print("   python main.py")
    print("")
    print("3. Enter your prompt when asked")
    print("")
    print("Available tools:")
    print("- bash: Execute shell commands")
    print("- str_replace_editor: Edit files")
    print("- terminate: End the conversation")
    print("- web_search: Search the web")
    print("- python_execute: Run Python code")
    print("")
    print("Note: Some tools (browser automation, MCP) are temporarily")
    print("disabled due to missing dependencies. Install them if needed:")
    print("- pip install browser-use")
    print("- pip install mcp")

async def main():
    """Run all tests."""
    print("OpenManus Final Setup Test")
    print("=" * 40)
    
    tests = [
        test_basic_functionality,
        test_agent_imports,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    # Check API key (not counted as a test)
    api_key_configured = check_api_key()
    
    print("\n" + "=" * 40)
    print(f"Core Tests: {passed}/{total} passed")
    
    if passed == total:
        print("✓ OpenManus core functionality is working!")
        show_usage_instructions()
        return 0
    else:
        print("✗ Some core tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
