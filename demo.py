#!/usr/bin/env python3
"""
OpenManus Demo Script
Demonstrates basic functionality without requiring API keys.
"""

import asyncio
import sys
from pathlib import Path

async def demo_tools():
    """Demonstrate available tools."""
    print("🔧 工具演示")
    print("=" * 40)
    
    # Demonstrate file operations
    print("\n📁 文件操作演示:")
    try:
        from app.tool import StrReplaceEditor
        from app.config import config
        
        editor = StrReplaceEditor()
        workspace = Path(config.workspace_root)
        demo_file = workspace / "demo.txt"
        
        # Create a demo file
        content = """# OpenManus 演示文件

这是一个由 OpenManus 创建的演示文件。

OpenManus 可以：
- 创建和编辑文件
- 执行系统命令
- 搜索网络信息
- 运行Python代码
- 进行专利搜索

感谢使用 OpenManus！
"""
        
        await editor.execute(command="create", path=str(demo_file), file_text=content)
        print(f"✓ 创建文件: {demo_file.name}")
        
        # Read the file
        result = await editor.execute(command="view", path=str(demo_file))
        print("✓ 文件内容:")
        print(result[:200] + "..." if len(result) > 200 else result)
        
    except Exception as e:
        print(f"✗ 文件操作失败: {e}")
    
    # Demonstrate terminate tool
    print("\n🛑 终止工具演示:")
    try:
        from app.tool import Terminate
        terminate = Terminate()
        result = await terminate.execute(status="success")
        print(f"✓ {result}")
    except Exception as e:
        print(f"✗ 终止工具失败: {e}")

def show_configuration():
    """Show current configuration."""
    print("\n⚙️ 当前配置")
    print("=" * 40)
    
    try:
        from app.config import config
        llm_config = config.llm['default']
        
        print(f"LLM 模型: {llm_config.model}")
        print(f"API 端点: {llm_config.base_url}")
        print(f"最大令牌: {llm_config.max_tokens}")
        print(f"温度: {llm_config.temperature}")
        
        if llm_config.api_key == "YOUR_OPENAI_API_KEY":
            print("API 密钥: ⚠️ 未配置 (使用占位符)")
        else:
            print("API 密钥: ✓ 已配置")
            
        print(f"工作空间: {config.workspace_root}")
        
    except Exception as e:
        print(f"✗ 配置读取失败: {e}")

def show_available_tools():
    """Show available tools."""
    print("\n🛠️ 可用工具")
    print("=" * 40)
    
    try:
        from app.tool import (
            Bash, StrReplaceEditor, Terminate, 
            WebSearch, PatentSearch, PythonExecute
        )
        
        tools = [
            (Bash(), "执行系统命令"),
            (StrReplaceEditor(), "文件编辑和查看"),
            (Terminate(), "结束对话"),
            (WebSearch(), "网络搜索"),
            (PatentSearch(), "专利搜索"),
            (PythonExecute(), "Python代码执行"),
        ]
        
        for tool, description in tools:
            print(f"✓ {tool.name}: {description}")
            
    except Exception as e:
        print(f"✗ 工具加载失败: {e}")

def show_usage_guide():
    """Show usage guide."""
    print("\n📖 使用指南")
    print("=" * 40)
    
    print("""
要开始使用 OpenManus:

1. 配置 API 密钥
   编辑 config/config.toml 文件:
   api_key = "your-actual-api-key"

2. 运行主程序
   python main.py

3. 输入您的请求
   例如: "帮我创建一个Python脚本来计算斐波那契数列"

4. 享受AI助手的帮助！

支持的API提供商:
- OpenAI (默认)
- Azure OpenAI  
- Anthropic Claude
- AWS Bedrock
- Ollama (本地)

更多信息请查看 SETUP_GUIDE.md
""")

async def main():
    """Run the demo."""
    print("🤖 OpenManus 演示")
    print("=" * 50)
    print("欢迎使用 OpenManus - 开源AI助手!")
    print("=" * 50)
    
    # Show configuration
    show_configuration()
    
    # Show available tools
    show_available_tools()
    
    # Demonstrate tools
    await demo_tools()
    
    # Show usage guide
    show_usage_guide()
    
    print("\n" + "=" * 50)
    print("演示完成! 🎉")
    print("准备好配置API密钥并开始使用 OpenManus 了吗?")
    print("运行: python main.py")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
