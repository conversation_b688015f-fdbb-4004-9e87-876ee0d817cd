# OpenManus Setup Guide

## 项目状态

✅ **基本配置完成** - OpenManus 核心功能已经配置完毕并可以运行！

## 当前配置

- **Python版本**: 3.8.6 (项目推荐 3.11-3.13，但当前版本可用)
- **核心依赖**: 已安装并配置
- **配置文件**: `config/config.toml` 已创建
- **工具系统**: 正常工作

## 快速开始

### 1. 配置 API 密钥

编辑 `config/config.toml` 文件，将 API 密钥替换为您的实际密钥：

```toml
[llm]
model = "gpt-4o"
base_url = "https://api.openai.com/v1"
api_key = "sk-your-actual-openai-api-key-here"  # 替换这里
max_tokens = 4096
temperature = 0.0
```

### 2. 运行应用

```bash
python main.py
```

### 3. 开始使用

程序启动后，输入您的提示词，例如：
- "帮我创建一个Python脚本"
- "搜索关于机器学习的信息"
- "帮我分析这个文件"

## 可用工具

当前已配置的工具：

- ✅ **bash**: 执行shell命令
- ✅ **str_replace_editor**: 文件编辑和查看
- ✅ **terminate**: 结束对话
- ✅ **web_search**: 网络搜索
- ✅ **python_execute**: 执行Python代码
- ✅ **patent_search**: 专利搜索

## 暂时禁用的功能

由于网络或依赖问题，以下功能暂时禁用：

- ⚠️ **browser_use_tool**: 浏览器自动化 (需要 `pip install browser-use`)
- ⚠️ **webpage_extractor**: 网页内容提取 (依赖browser_use)
- ⚠️ **mcp_agent**: MCP协议支持 (需要 `pip install mcp`)

## 安装额外依赖 (可选)

如果您需要浏览器自动化功能：

```bash
pip install browser-use
playwright install
```

如果您需要MCP支持：

```bash
pip install mcp
```

## 测试脚本

项目包含几个测试脚本：

- `test_basic.py`: 基本导入和配置测试
- `final_test.py`: 核心功能测试
- `simple_test.py`: 组件测试

运行测试：
```bash
python final_test.py
```

## 故障排除

### 1. 网络连接问题

如果遇到代理错误，可能是网络配置问题。项目在离线模式下的核心功能已经测试通过。

### 2. Python版本警告

项目显示"不支持的Python版本"警告，但在Python 3.8.6下仍可正常工作。如果遇到问题，建议升级到Python 3.11+。

### 3. 依赖问题

如果某些功能不可用，检查是否安装了相应的依赖包。

## 项目结构

```
OpenManus/
├── app/                 # 主应用代码
│   ├── agent/          # 智能代理
│   ├── tool/           # 工具集合
│   ├── config.py       # 配置管理
│   └── llm.py          # LLM接口
├── config/             # 配置文件
│   └── config.toml     # 主配置文件
├── workspace/          # 工作空间
├── main.py            # 主入口
└── README.md          # 项目说明
```

## 支持的LLM提供商

项目支持多种LLM提供商，在 `config/config.toml` 中配置：

- OpenAI (默认)
- Azure OpenAI
- Anthropic Claude
- AWS Bedrock
- Ollama (本地)

## 下一步

1. 设置您的API密钥
2. 运行 `python main.py`
3. 开始与OpenManus交互！

享受使用OpenManus！🚀
