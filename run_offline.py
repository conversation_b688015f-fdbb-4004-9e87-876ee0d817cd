#!/usr/bin/env python3
"""
Offline test runner for OpenManus that doesn't require network access.
This script tests the core functionality without making API calls.
"""

import asyncio
import sys
from unittest.mock import AsyncMock, MagicMock, patch

async def test_agent_creation():
    """Test if we can create a Manus agent without network calls."""
    print("Testing agent creation...")
    
    try:
        # Mock the LLM class to avoid network calls
        with patch('app.llm.LLM') as mock_llm_class:
            # Create a mock LLM instance
            mock_llm = AsyncMock()
            mock_llm.ask = AsyncMock(return_value=MagicMock(
                content="Hello! I'm a test response.",
                tool_calls=None
            ))
            mock_llm_class.return_value = mock_llm
            
            # Mock tiktoken to avoid network calls
            with patch('tiktoken.encoding_for_model') as mock_tiktoken:
                mock_encoding = MagicMock()
                mock_encoding.encode.return_value = [1, 2, 3, 4, 5]  # Mock token list
                mock_tiktoken.return_value = mock_encoding
                
                from app.agent.manus import Manus
                
                # Create agent
                agent = Manus()
                print(f"✓ Agent created successfully: {agent.name}")
                print(f"  - Available tools: {len(agent.available_tools.tools)}")
                print(f"  - Tool names: {[tool.name for tool in agent.available_tools.tools]}")
                
                return agent
                
    except Exception as e:
        print(f"✗ Failed to create agent: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_tool_execution():
    """Test basic tool execution."""
    print("\nTesting tool execution...")
    
    try:
        from app.tool import Bash, Terminate
        
        # Test Terminate tool (safe to execute)
        terminate_tool = Terminate()
        result = await terminate_tool.execute(reason="Test termination")
        print(f"✓ Terminate tool executed: {result.output}")
        
        # Test Bash tool with a simple command
        bash_tool = Bash()
        result = await bash_tool.execute(command="echo 'Hello from OpenManus!'")
        print(f"✓ Bash tool executed: {result.output.strip()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to test tools: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_interaction():
    """Test agent interaction without API calls."""
    print("\nTesting agent interaction...")
    
    try:
        # Mock the LLM class to avoid network calls
        with patch('app.llm.LLM') as mock_llm_class:
            # Create a mock LLM instance
            mock_llm = AsyncMock()
            mock_llm.ask = AsyncMock(return_value=MagicMock(
                content="I understand you want to test the system. This is a mock response.",
                tool_calls=None
            ))
            mock_llm_class.return_value = mock_llm
            
            # Mock tiktoken to avoid network calls
            with patch('tiktoken.encoding_for_model') as mock_tiktoken:
                mock_encoding = MagicMock()
                mock_encoding.encode.return_value = [1, 2, 3, 4, 5]
                mock_tiktoken.return_value = mock_encoding
                
                from app.agent.manus import Manus
                
                # Create agent
                agent = Manus()
                
                # Test a simple interaction
                test_prompt = "Hello, can you help me test the system?"
                
                # Mock the agent's run method to avoid full execution
                with patch.object(agent, 'think', return_value=True) as mock_think:
                    with patch.object(agent, 'act', return_value=True) as mock_act:
                        print(f"✓ Agent interaction test setup complete")
                        print(f"  - Test prompt: {test_prompt}")
                        print(f"  - Mock LLM response ready")
                        
                        # Test that we can call the mocked methods
                        think_result = await agent.think()
                        act_result = await agent.act()
                        
                        print(f"  - Think result: {think_result}")
                        print(f"  - Act result: {act_result}")
                        
                        return True
                
    except Exception as e:
        print(f"✗ Failed to test agent interaction: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all offline tests."""
    print("OpenManus Offline Test Runner")
    print("=" * 50)
    print("This test runs without making any network calls.")
    print("=" * 50)
    
    tests = [
        test_agent_creation,
        test_tool_execution,
        test_agent_interaction,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            result = await test()
            if result is not None and result is not False:
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All offline tests passed! OpenManus core functionality is working.")
        print("\nTo run with real API calls:")
        print("1. Set your API key in config/config.toml:")
        print("   api_key = \"your-actual-api-key-here\"")
        print("2. Run: python main.py")
        print("3. Enter your prompt when asked")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
