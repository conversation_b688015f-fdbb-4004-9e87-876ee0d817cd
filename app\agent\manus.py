from typing import List, Optional

from pydantic import Field, model_validator

from app.agent.browser import <PERSON><PERSON><PERSON><PERSON><PERSON>xtHelper
from app.agent.toolcall import Tool<PERSON>allAgent
from app.config import config
from app.prompt.manus import NEXT_STEP_PROMPT, SYSTEM_PROMPT
from app.tool import Terminate, Tool<PERSON>ollection
# from app.tool.browser_use_tool import BrowserU<PERSON>Tool  # Temporarily disabled
from app.tool.patent_search import PatentSearch
from app.tool.python_execute import Python<PERSON>xecute
from app.tool.str_replace_editor import StrReplaceEditor
# from app.tool.webpage_extractor import WebpageExtractor  # Temporarily disabled


class Manus(ToolCallAgent):
    """A versatile general-purpose agent."""

    name: str = "Manus"
    description: str = (
        "A versatile agent that can solve various tasks using multiple tools"
    )

    system_prompt: str = SYSTEM_PROMPT.format(directory=config.workspace_root)
    next_step_prompt: str = NEXT_STEP_PROMPT

    max_observe: int = 10000
    max_steps: int = 20

    # Add general-purpose tools to the tool collection (some tools temporarily disabled)
    available_tools: ToolCollection = Field(
        default_factory=lambda: ToolCollection(
            PythonExecute(), StrReplaceEditor(), PatentSearch(), Terminate()
        )
    )

    special_tool_names: List[str] = Field(default_factory=lambda: [Terminate().name])

    browser_context_helper: Optional[BrowserContextHelper] = None

    @model_validator(mode="after")
    def initialize_helper(self) -> "Manus":
        self.browser_context_helper = BrowserContextHelper(self)
        return self

    async def think(self) -> bool:
        """Process current state and decide next actions with appropriate context."""
        original_prompt = self.next_step_prompt
        recent_messages = self.memory.messages[-3:] if self.memory.messages else []
        # BrowserUseTool temporarily disabled, so browser_in_use is always False
        browser_in_use = False

        if browser_in_use:
            self.next_step_prompt = (
                await self.browser_context_helper.format_next_step_prompt()
            )

        result = await super().think()

        # Restore original prompt
        self.next_step_prompt = original_prompt

        return result

    async def cleanup(self):
        """Clean up Manus agent resources."""
        if self.browser_context_helper:
            await self.browser_context_helper.cleanup_browser()
