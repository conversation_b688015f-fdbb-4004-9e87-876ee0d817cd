#!/usr/bin/env python3
"""
Basic test script to verify OpenManus setup without network dependencies.
"""

import sys
import os

def test_imports():
    """Test if all basic modules can be imported."""
    print("Testing basic imports...")

    try:
        from app.config import config
        print("✓ Config module imported successfully")
    except Exception as e:
        print(f"✗ Failed to import config: {e}")
        return False

    try:
        from app.logger import logger
        print("✓ Logger module imported successfully")
    except Exception as e:
        print(f"✗ Failed to import logger: {e}")
        return False

    try:
        from app.tool import BaseTool, Bash, Terminate
        print("✓ Basic tools imported successfully")
    except Exception as e:
        print(f"✗ Failed to import basic tools: {e}")
        return False

    try:
        from app.agent.base import BaseAgent
        print("✓ Base agent imported successfully")
    except Exception as e:
        print(f"✗ Failed to import base agent: {e}")
        return False

    return True

def test_config():
    """Test configuration loading."""
    print("\nTesting configuration...")

    try:
        from app.config import config
        print(f"✓ Config loaded successfully")

        # Access the default LLM configuration
        default_llm = config.llm['default']
        print(f"  - LLM model: {default_llm.model}")
        print(f"  - LLM base_url: {default_llm.base_url}")
        print(f"  - API key configured: {'Yes' if default_llm.api_key != 'YOUR_OPENAI_API_KEY' else 'No (using placeholder)'}")
        return True
    except Exception as e:
        print(f"✗ Failed to load config: {e}")
        return False

def test_tools():
    """Test basic tool functionality."""
    print("\nTesting basic tools...")

    try:
        from app.tool import Bash, Terminate

        # Test Bash tool
        bash_tool = Bash()
        print(f"✓ Bash tool created: {bash_tool.name}")

        # Test Terminate tool
        terminate_tool = Terminate()
        print(f"✓ Terminate tool created: {terminate_tool.name}")

        return True
    except Exception as e:
        print(f"✗ Failed to test tools: {e}")
        return False

def main():
    """Run all tests."""
    print("OpenManus Basic Setup Test")
    print("=" * 40)

    tests = [
        test_imports,
        test_config,
        test_tools,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")

    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("✓ All tests passed! OpenManus basic setup is working.")
        print("\nNext steps:")
        print("1. Configure your API key in config/config.toml")
        print("2. Install missing dependencies if needed:")
        print("   - pip install browser-use (for browser automation)")
        print("   - pip install mcp (for MCP support)")
        print("3. Run: python main.py")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
